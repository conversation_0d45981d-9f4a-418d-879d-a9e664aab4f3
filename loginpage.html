<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Page</title>
</head>
<style>
    *{
        margin: 0px;
        padding: 0px;
        box-sizing: border-box;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    body{
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        background: lightblue;
    }
    .login-container{
        background-color: white;
        padding: 2.5rem;
        border-radius: 15px;
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        width: 350px;
        text-align: center;
        animation: fadeIn 0.7s ease-in-out;
        border: 1px solid rgba(255,255,255,0.3);
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    h2 {
        color: #333;
        margin-bottom: 1.5rem;
        font-size: 1.8rem;
        font-weight: 600;
    }

    .input-group {
        margin-bottom: 1.2rem;
        text-align: left;
    }

    label {
        display: block;
        margin-bottom: 0.5rem;
        color: #555;
        font-weight: 500;
        font-size: 0.9rem;
        text-transform: capitalize;
    }

    input[type="text"], input[type="password"] {
        width: 100%;
        padding: 0.8rem;
        border: 2px solid #e1e1e1;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: #f9f9f9;
    }

    input[type="text"]:focus, input[type="password"]:focus {
        outline: none;
        border-color: #4CAF50;
        background-color: white;
        box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        transform: translateY(-1px);
    }

    button {
        width: 100%;
        padding: 0.9rem;
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    button:hover {
        background: linear-gradient(135deg, #45a049, #3d8b40);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }

    button:active {
        transform: translateY(0);
    }

    .signup-link {
        margin-top: 1.5rem;
        color: #666;
        font-size: 0.9rem;
    }

    .signup-link a {
        color: #4CAF50;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .signup-link a:hover {
        color: #45a049;
        text-decoration: underline;
    }

    /* Responsive design */
    @media (max-width: 480px) {
        .login-container {
            width: 90%;
            padding: 2rem;
        }

        h2 {
            font-size: 1.6rem;
        }
    }

</style>
<body>
    <div class="login-container">
        <h2>Login</h2>
        <form action="">
            <div class="input-group">
                <label for="username">username: </label>
                <input type="text" placeholder="enter username" required>
            </div>
            <div class="input-group">
                <label for="password">password: </label>
                <input type="password" placeholder="enter password" required>
            </div>
            <button type="submit">Login</button>
            <p class="signup-link">Don't have a account. <a href="#">Sign Up</a></p>
        </form>
    </div>
</body>
</html>